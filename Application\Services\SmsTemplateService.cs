using Application.DTOs.Sms;
using System;
using System.Collections.Generic;
using System.Linq;

namespace Application.Services
{
    public interface ISmsTemplateService
    {
        SmsTemplate GetTemplate(SmsTemplateType templateType);
        string FormatMessage(SmsTemplate template, Dictionary<string, string> placeholderValues);
        SmsTemplate GetRegistrationOtpTemplate();
        SmsTemplate GetLoginOtpTemplate();
        SmsTemplate GetBookingConfirmationTemplate();
    }

    public class SmsTemplateService : ISmsTemplateService
    {
        private readonly Dictionary<SmsTemplateType, SmsTemplate> _templates;

        public SmsTemplateService()
        {
            _templates = InitializeTemplates();
        }

        private Dictionary<SmsTemplateType, SmsTemplate> InitializeTemplates()
        {
            return new Dictionary<SmsTemplateType, SmsTemplate>
            {
                {
                    SmsTemplateType.RegistrationOtp,
                    new SmsTemplate
                    {
                        TemplateId = "1707175265803358380",
                        Content = "{#var#} is your OTP to complete registration with ONEWAY TRIP CARTS. Do not share this code with anyone.",
                        Type = SmsTemplateType.RegistrationOtp,
                        PlaceholderKeys = new List<string> { "otp" }
                    }
                },
                {
                    SmsTemplateType.LoginOtp,
                    new SmsTemplate
                    {
                        TemplateId = "1707174868881892651",
                        Content = "{#var#} is your OTP for login. Do not share this OTP with anyone ONEWAY TRIP CARTS",
                        Type = SmsTemplateType.LoginOtp,
                        PlaceholderKeys = new List<string> { "otp" }
                    }
                },
                {
                    SmsTemplateType.BookingConfirmation,
                    new SmsTemplate
                    {
                        TemplateId = "1707175265777482559",
                        Content = "Booking is Confirmed! From: {#var#} To: {#var#} Date: {#var#} | Time: {#var#} Cab: {#var#}. We will share driver details shortly - ONEWAY TRIP CARTS",
                        Type = SmsTemplateType.BookingConfirmation,
                        PlaceholderKeys = new List<string> { "from", "to", "date", "time", "cab" }
                    }
                }
            };
        }

        public SmsTemplate GetTemplate(SmsTemplateType templateType)
        {
            return _templates.TryGetValue(templateType, out var template) ? template : null;
        }

        public string FormatMessage(SmsTemplate template, Dictionary<string, string> placeholderValues)
        {
            if (template == null)
                throw new ArgumentNullException(nameof(template));

            var message = template.Content;
            
            // Replace placeholders in order for templates with multiple {#var#} placeholders
            if (template.Type == SmsTemplateType.BookingConfirmation)
            {
                // For booking confirmation, replace in specific order
                var orderedKeys = new[] { "from", "to", "date", "time", "cab" };
                foreach (var key in orderedKeys)
                {
                    if (placeholderValues.ContainsKey(key))
                    {
                        // Replace the first occurrence of {#var#}
                        var index = message.IndexOf("{#var#}");
                        if (index >= 0)
                        {
                            message = message.Substring(0, index) + placeholderValues[key] + message.Substring(index + 7);
                        }
                    }
                }
            }
            else
            {
                // For OTP templates, replace single {#var#} with OTP
                if (placeholderValues.ContainsKey("otp"))
                {
                    message = message.Replace("{#var#}", placeholderValues["otp"]);
                }
            }

            return message;
        }

        public SmsTemplate GetRegistrationOtpTemplate()
        {
            return GetTemplate(SmsTemplateType.RegistrationOtp);
        }

        public SmsTemplate GetLoginOtpTemplate()
        {
            return GetTemplate(SmsTemplateType.LoginOtp);
        }

        public SmsTemplate GetBookingConfirmationTemplate()
        {
            return GetTemplate(SmsTemplateType.BookingConfirmation);
        }
    }
}
