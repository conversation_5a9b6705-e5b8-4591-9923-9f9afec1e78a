using System;
using System.Collections.Generic;

namespace Application.DTOs.Sms
{
    public class SmsTemplate
    {
        public string TemplateId { get; set; }
        public string Content { get; set; }
        public SmsTemplateType Type { get; set; }
        public List<string> PlaceholderKeys { get; set; } = new List<string>();
    }

    public enum SmsTemplateType
    {
        RegistrationOtp,
        LoginOtp,
        BookingConfirmation
    }

    public class SmsTemplateRequest
    {
        public string PhoneNumber { get; set; }
        public SmsTemplateType TemplateType { get; set; }
        public Dictionary<string, string> PlaceholderValues { get; set; } = new Dictionary<string, string>();
    }

    public class BookingConfirmationSmsData
    {
        public string FromLocation { get; set; }
        public string ToLocation { get; set; }
        public string Date { get; set; }
        public string Time { get; set; }
        public string CabType { get; set; }
        public string PhoneNumber { get; set; }
    }
}
