using Application.DTOs.Sms;
using Application.Interfaces;
using Application.Services;
using Domain.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;
using System.Web;

namespace Infrastructure.Shared.Services
{
    public class SmsService : ISmsService
    {
        private readonly SmsSettings _smsSettings;
        private readonly ILogger<SmsService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ISmsTemplateService _templateService;

        public SmsService(IOptions<SmsSettings> smsSettings, ILogger<SmsService> logger, IHttpClientFactory httpClientFactory, ISmsTemplateService templateService)
        {
            _smsSettings = smsSettings.Value;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _templateService = templateService;
        }

        public async Task<bool> SendOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                var message = $"Your CabYaari verification code is: {otp}. Do not share this code with anyone.";
                var smsRequest = new SmsRequest
                {
                    PhoneNumbers = phoneNumber,
                    SmsTemplateBody = message
                };

                return await SendSmsAsync(smsRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendSmsAsync(SmsRequest request)
        {
            try
            {
                _logger.LogInformation("Sending SMS to {PhoneNumber}", request.PhoneNumbers);

                var parameters = new Dictionary<string, string>
                {
                    {"apikey", _smsSettings.OTPAPIKey},
                    {"numbers", request.PhoneNumbers},
                    {"message", request.SmsTemplateBody},
                    {"sender", _smsSettings.Sender}
                };

                var encodedContent = new FormUrlEncodedContent(parameters);

                using var httpClient = _httpClientFactory.CreateClient();
                var response = await httpClient.PostAsync(_smsSettings.OTPAPI, encodedContent);
                var responseContent = await response.Content.ReadAsStringAsync();

                _logger.LogInformation("SMS API Response: {Response}", responseContent);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("SMS sent successfully to {PhoneNumber}", request.PhoneNumbers);
                    return true;
                }
                else
                {
                    _logger.LogError("Failed to send SMS. Status: {StatusCode}, Response: {Response}",
                        response.StatusCode, responseContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS to {PhoneNumber}", request.PhoneNumbers);
                return false;
            }
        }

        public async Task<bool> SendTemplatedSmsAsync(SmsTemplateRequest request)
        {
            try
            {
                var template = _templateService.GetTemplate(request.TemplateType);
                if (template == null)
                {
                    _logger.LogError("Template not found for type {TemplateType}", request.TemplateType);
                    return false;
                }

                var message = _templateService.FormatMessage(template, request.PlaceholderValues);
                var smsRequest = new SmsRequest
                {
                    PhoneNumbers = request.PhoneNumber,
                    SmsTemplateBody = message
                };

                return await SendSmsAsync(smsRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending templated SMS to {PhoneNumber}", request.PhoneNumber);
                return false;
            }
        }

        public async Task<bool> SendRegistrationOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                var template = _templateService.GetRegistrationOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                var smsRequest = new SmsRequest
                {
                    PhoneNumbers = phoneNumber,
                    SmsTemplateBody = message
                };

                return await SendSmsAsync(smsRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending registration OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendLoginOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                var template = _templateService.GetLoginOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                var smsRequest = new SmsRequest
                {
                    PhoneNumbers = phoneNumber,
                    SmsTemplateBody = message
                };

                return await SendSmsAsync(smsRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending login OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendBookingConfirmationAsync(BookingConfirmationSmsData bookingData)
        {
            try
            {
                var template = _templateService.GetBookingConfirmationTemplate();
                var placeholderValues = new Dictionary<string, string>
                {
                    { "from", bookingData.FromLocation },
                    { "to", bookingData.ToLocation },
                    { "date", bookingData.Date },
                    { "time", bookingData.Time },
                    { "cab", bookingData.CabType }
                };

                var message = _templateService.FormatMessage(template, placeholderValues);
                var smsRequest = new SmsRequest
                {
                    PhoneNumbers = bookingData.PhoneNumber,
                    SmsTemplateBody = message
                };

                return await SendSmsAsync(smsRequest);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending booking confirmation SMS to {PhoneNumber}", bookingData.PhoneNumber);
                return false;
            }
        }
    }
}
