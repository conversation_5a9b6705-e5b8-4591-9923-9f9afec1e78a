using Application.DTOs.Sms;
using Application.Interfaces;
using Application.Services;
using Domain.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;

namespace Infrastructure.Shared.Services
{
    public class ICloudSmsService : ISmsService
    {
        private readonly ICloudSmsSettings _iCloudSmsSettings;
        private readonly ILogger<ICloudSmsService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ISmsTemplateService _templateService;

        public ICloudSmsService(
            IOptions<ICloudSmsSettings> iCloudSmsSettings,
            ILogger<ICloudSmsService> logger,
            IHttpClientFactory httpClientFactory,
            ISmsTemplateService templateService)
        {
            _iCloudSmsSettings = iCloudSmsSettings.Value;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _templateService = templateService;
        }

        public async Task<bool> SendOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                // Use login OTP template by default for backward compatibility
                var template = _templateService.GetLoginOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                return await SendTemplatedSmsAsync(phoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendSmsAsync(SmsRequest request)
        {
            try
            {
                // For backward compatibility, send without template ID
                return await SendTemplatedSmsAsync(request.PhoneNumbers, request.SmsTemplateBody, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS to {PhoneNumber}", request.PhoneNumbers);
                return false;
            }
        }

        public async Task<bool> SendTemplatedSmsAsync(SmsTemplateRequest request)
        {
            try
            {
                var template = _templateService.GetTemplate(request.TemplateType);
                if (template == null)
                {
                    _logger.LogError("Template not found for type {TemplateType}", request.TemplateType);
                    return false;
                }

                var message = _templateService.FormatMessage(template, request.PlaceholderValues);
                return await SendTemplatedSmsAsync(request.PhoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending templated SMS to {PhoneNumber}", request.PhoneNumber);
                return false;
            }
        }

        public async Task<bool> SendRegistrationOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                var template = _templateService.GetRegistrationOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                Console.WriteLine($"Sending registration OTP to {phoneNumber} with message: {message}");

                return await SendTemplatedSmsAsync(phoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending registration OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendLoginOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                var template = _templateService.GetLoginOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                return await SendTemplatedSmsAsync(phoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending login OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendBookingConfirmationAsync(BookingConfirmationSmsData bookingData)
        {
            try
            {
                var template = _templateService.GetBookingConfirmationTemplate();
                var placeholderValues = new Dictionary<string, string>
                {
                    { "from", bookingData.FromLocation },
                    { "to", bookingData.ToLocation },
                    { "date", bookingData.Date },
                    { "time", bookingData.Time },
                    { "cab", bookingData.CabType }
                };

                var message = _templateService.FormatMessage(template, placeholderValues);
                return await SendTemplatedSmsAsync(bookingData.PhoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending booking confirmation SMS to {PhoneNumber}", bookingData.PhoneNumber);
                return false;
            }
        }

        private async Task<bool> SendTemplatedSmsAsync(string phoneNumber, string message, string templateId)
        {
            try
            {
                Console.WriteLine("Sending SMS to {PhoneNumber} using iCloudSMS", phoneNumber);

                // Build the URL with query parameters as per the provided curl example
                var baseUrl = _iCloudSmsSettings.ApiUrl;
                var urlBuilder = new UriBuilder(baseUrl);
                var query = HttpUtility.ParseQueryString(urlBuilder.Query);
                
                query["AUTH_KEY"] = _iCloudSmsSettings.AuthKey;
                query["message"] = message;
                query["senderId"] = _iCloudSmsSettings.SenderId;
                query["routeId"] = _iCloudSmsSettings.RouteId;
                query["mobileNos"] = phoneNumber;
                query["smsContentType"] = _iCloudSmsSettings.SmsContentType;
                query["tmid"] = _iCloudSmsSettings.TmId;
                query["concentFailoverId"] = _iCloudSmsSettings.ConcentFailoverId;
                
                if (!string.IsNullOrEmpty(templateId))
                {
                    query["templateid"] = templateId;
                }

                urlBuilder.Query = query.ToString();
                var requestUrl = urlBuilder.ToString();

                Console.WriteLine("iCloudSMS Request URL: {RequestUrl}", requestUrl);

                using var httpClient = _httpClientFactory.CreateClient();

                var response = await httpClient.GetAsync(requestUrl);
                var responseContent = await response.Content.ReadAsStringAsync();

                Console.WriteLine("iCloudSMS API Response: Status={StatusCode}, Content={Response}", 
                    response.StatusCode, responseContent);

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine("SMS sent successfully to {PhoneNumber} via iCloudSMS", phoneNumber);
                    return true;
                }
                else
                {
                    Console.WriteLine("Failed to send SMS via iCloudSMS. Status: {StatusCode}, Response: {Response}", 
                        response.StatusCode, responseContent);
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine(ex);
                Console.WriteLine("Error sending SMS to {PhoneNumber} via iCloudSMS: {Error}", 
                    phoneNumber, ex.Message);
                _logger.LogError(ex, "Error sending SMS to {PhoneNumber} via iCloudSMS", phoneNumber);
                return false;
            }
        }
    }
}
