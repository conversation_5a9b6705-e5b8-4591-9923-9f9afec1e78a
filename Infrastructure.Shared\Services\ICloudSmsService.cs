using Application.DTOs.Sms;
using Application.Interfaces;
using Application.Services;
using Domain.Settings;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Web;

namespace Infrastructure.Shared.Services
{
    public class ICloudSmsService : ISmsService
    {
        private readonly ICloudSmsSettings _iCloudSmsSettings;
        private readonly ILogger<ICloudSmsService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly ISmsTemplateService _templateService;

        public ICloudSmsService(
            IOptions<ICloudSmsSettings> iCloudSmsSettings,
            ILogger<ICloudSmsService> logger,
            IHttpClientFactory httpClientFactory,
            ISmsTemplateService templateService)
        {
            _iCloudSmsSettings = iCloudSmsSettings.Value;
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _templateService = templateService;
        }

        public async Task<bool> SendOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                // Use login OTP template by default for backward compatibility
                var template = _templateService.GetLoginOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                return await SendTemplatedSmsAsync(phoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendSmsAsync(SmsRequest request)
        {
            try
            {
                // For backward compatibility, send without template ID
                return await SendTemplatedSmsAsync(request.PhoneNumbers, request.SmsTemplateBody, null);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending SMS to {PhoneNumber}", request.PhoneNumbers);
                return false;
            }
        }

        public async Task<bool> SendTemplatedSmsAsync(SmsTemplateRequest request)
        {
            try
            {
                var template = _templateService.GetTemplate(request.TemplateType);
                if (template == null)
                {
                    _logger.LogError("Template not found for type {TemplateType}", request.TemplateType);
                    return false;
                }

                var message = _templateService.FormatMessage(template, request.PlaceholderValues);
                return await SendTemplatedSmsAsync(request.PhoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending templated SMS to {PhoneNumber}", request.PhoneNumber);
                return false;
            }
        }

        public async Task<bool> SendRegistrationOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                var template = _templateService.GetRegistrationOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                Console.WriteLine($"Sending registration OTP to {phoneNumber} with message: {message}");

                return await SendTemplatedSmsAsync(phoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending registration OTP to {phoneNumber}: {ex.Message}");
                _logger.LogError(ex, "Error sending registration OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendLoginOtpAsync(string phoneNumber, string otp)
        {
            try
            {
                var template = _templateService.GetLoginOtpTemplate();
                var placeholderValues = new Dictionary<string, string> { { "otp", otp } };
                var message = _templateService.FormatMessage(template, placeholderValues);

                return await SendTemplatedSmsAsync(phoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending login OTP to {PhoneNumber}", phoneNumber);
                return false;
            }
        }

        public async Task<bool> SendBookingConfirmationAsync(BookingConfirmationSmsData bookingData)
        {
            try
            {
                var template = _templateService.GetBookingConfirmationTemplate();
                var placeholderValues = new Dictionary<string, string>
                {
                    { "from", bookingData.FromLocation },
                    { "to", bookingData.ToLocation },
                    { "date", bookingData.Date },
                    { "time", bookingData.Time },
                    { "cab", bookingData.CabType }
                };

                var message = _templateService.FormatMessage(template, placeholderValues);
                return await SendTemplatedSmsAsync(bookingData.PhoneNumber, message, template.TemplateId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error sending booking confirmation SMS to {PhoneNumber}", bookingData.PhoneNumber);
                return false;
            }
        }

        private async Task<bool> SendTemplatedSmsAsync(string phoneNumber, string message, string templateId)
        {
            try
            {
                Console.WriteLine($"Sending SMS to {phoneNumber} using iCloudSMS");

                // Build the URL with query parameters as per the provided curl example
                var baseUrl = _iCloudSmsSettings.ApiUrl;

                // Manually build query string with proper URL encoding (%20 instead of +)
                var queryParams = new List<string>
                {
                    $"AUTH_KEY={Uri.EscapeDataString(_iCloudSmsSettings.AuthKey)}",
                    $"message={Uri.EscapeDataString(message)}",
                    $"senderId={Uri.EscapeDataString(_iCloudSmsSettings.SenderId)}",
                    $"routeId={Uri.EscapeDataString(_iCloudSmsSettings.RouteId)}",
                    $"mobileNos={Uri.EscapeDataString(phoneNumber)}",
                    $"smsContentType={Uri.EscapeDataString(_iCloudSmsSettings.SmsContentType)}",
                    $"tmid={Uri.EscapeDataString(_iCloudSmsSettings.TmId)}",
                    $"concentFailoverId={Uri.EscapeDataString(_iCloudSmsSettings.ConcentFailoverId)}"
                };

                if (!string.IsNullOrEmpty(templateId))
                {
                    queryParams.Add($"templateid={Uri.EscapeDataString(templateId)}");
                }

                var requestUrl = $"{baseUrl}?{string.Join("&", queryParams)}";

                Console.WriteLine($"iCloudSMS Request URL: {requestUrl}");

                using var httpClient = _httpClientFactory.CreateClient();

                var response = await httpClient.GetAsync(requestUrl);
                var responseContent = await response.Content.ReadAsStringAsync();

                Console.WriteLine($"iCloudSMS API Response: Status={response.StatusCode}, Content={responseContent}");

                if (response.IsSuccessStatusCode)
                {
                    Console.WriteLine($"SMS sent successfully to {phoneNumber} via iCloudSMS");
                    return true;
                }
                else
                {
                    Console.WriteLine($"Failed to send SMS via iCloudSMS. Status: {response.StatusCode}, Response: {responseContent}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Error sending SMS to {phoneNumber} via iCloudSMS: {ex.Message}");
                _logger.LogError(ex, "Error sending SMS to {PhoneNumber} via iCloudSMS", phoneNumber);
                return false;
            }
        }
    }
}
