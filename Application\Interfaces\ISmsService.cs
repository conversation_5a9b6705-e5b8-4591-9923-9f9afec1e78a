using Application.DTOs.Sms;
using System.Threading.Tasks;

namespace Application.Interfaces
{
    public interface ISmsService
    {
        Task<bool> SendOtpAsync(string phoneNumber, string otp);
        Task<bool> SendSmsAsync(SmsRequest request);
        Task<bool> SendTemplatedSmsAsync(SmsTemplateRequest request);
        Task<bool> SendRegistrationOtpAsync(string phoneNumber, string otp);
        Task<bool> SendLoginOtpAsync(string phoneNumber, string otp);
        Task<bool> SendBookingConfirmationAsync(BookingConfirmationSmsData bookingData);
    }
}
