﻿using Application.Interfaces;
using Application.Services;
using Domain.Settings;
using Infrastructure.Shared.Services;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;

namespace Infrastructure.Shared
{
    public static class ServiceRegistration
    {
        public static void AddSharedInfrastructure(this IServiceCollection services, IConfiguration _config)
        {
            services.Configure<MailSettings>(_config.GetSection("MailSettings"));
            services.Configure<MapSettings>(_config.GetSection("MapSettings"));
            services.Configure<PaymentSettings>(_config.GetSection("PaymentSettings"));
            services.Configure<SmsSettings>(_config.GetSection("SmsSettings"));
            services.Configure<ICloudSmsSettings>(_config.GetSection("ICloudSmsSettings"));

            services.AddTransient<IDateTimeService, DateTimeService>();
            services.AddTransient<IEmailService, EmailService>();
            services.AddTransient<ISmsTemplateService, SmsTemplateService>();

            // Register both SMS services - default is TextLocal, alternative is iCloudSMS
            services.AddTransient<SmsService>();
            services.AddTransient<ICloudSmsService>();

            // Use iCloudSMS as the default implementation
            services.AddTransient<ISmsService, ICloudSmsService>();
        }
    }
}
